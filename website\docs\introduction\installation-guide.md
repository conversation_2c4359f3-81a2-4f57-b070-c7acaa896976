---
id: installation-guide
title: Installation Guide
sidebar_label: Installation Guide
sidebar_position: 4
---

# Getting Started with Cloc SDK

Welcome to Cloc SDK! This comprehensive guide will help you integrate powerful time tracking and analytics capabilities into your Next.js application. Whether you're building a productivity tool, project management system, or team collaboration platform, Cloc SDK provides the building blocks you need.

## Prerequisites

- Node.js 16.x or later
- npm 7.x or later
- Basic familiarity with Next.js and TypeScript
- An Ever Cloc account with API access

## Authentication Setup

### 1. Obtain Your API Token

Before diving into the installation, you'll need to set up authentication:

1. Log in to your [Ever Cloc Dashboard](https://cloc.ai/)
2. Navigate to Settings → API Access
3. Click "Generate New Token"
4. Name your token (e.g., "Development Token")
5. Set appropriate permissions (minimum: timer:read, timer:write)
6. Copy the generated token

> 🔐 Security Tip: Your API token grants access to your Cloc account. Never share it or commit it to version control. Consider using a different token for development and production environments.

### 2. Configure Token Access

Create a `.env.local` file in your project root:

```bash
# Cloc API Configuration
NEXT_PUBLIC_CLOC_API_TOKEN=your_api_token_here
NEXT_PUBLIC_CLOC_API_URL=https://api.cloc.ai/v1

```

## Project Setup

### 1. Create a Next.js Project

Create a new Next.js application with our recommended configuration:

```bash
npx create-next-app@latest my-cloc-app

```

When prompted, select:

```bash
✔ Would you like to use TypeScript? Yes
✔ Would you like to use ESLint? Yes
✔ Would you like to use Tailwind CSS? Yes
✔ Would you like to use src/ directory? Yes
✔ Would you like to use App Router? Yes
✔ Would you like to customize the default import alias (@/*)? Yes

```

### 2. Install SDK Packages

Install the core Cloc SDK packages and their peer dependencies:

```bash
# Core SDK packages
npm install @cloc/atoms

```

## Configuration

### 1. Provider Setup

Create a provider wrapper in your root layout (`src/app/layout.tsx`):

```tsx
import { ClocProvider } from "@cloc/atoms";
import { useAccessToken } from "@cloc/atoms";

export default function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const { accessToken } = useAccessToken();

	return (
		<html lang="en">
			<body>
				<ClocProvider
					token={accessToken}
					theme={{
						colors: {
							primaryColor: "#4CAF50",
							secondaryColor: "#2196F3",
						},
					}}
				>
					{children}
				</ClocProvider>
			</body>
		</html>
	);
}
```

### 2. Tailwind CSS Configuration

Update your `tailwind.config.js` to include Cloc components:

```jsx
module.exports = {
	content: [
		"./src/**/*.{js,ts,jsx,tsx}",
		"./node_modules/@cloc/atoms/**/*.{js,ts,jsx,tsx}",
		"./node_modules/@cloc/ui/**/*.{js,ts,jsx,tsx}",
	],
	theme: {
		extend: {
			colors: {
				primary: "var(--cloc-primary)",
				secondary: "var(--cloc-secondary)",
				accent: "var(--cloc-accent)",
				background: "var(--cloc-background)",
			},
		},
	},
	plugins: [require("tailwindcss-animate")],
};
```

## Quick Start Example

Create a test component to verify your installation (`src/app/page.tsx`):

```tsx
import { ModernCloc } from "@cloc/atoms";

export default function HomePage() {
	return (
		<div className="flex min-h-screen items-center justify-center p-4">
			<ModernCloc
				expanded={false}
				showProgress={true}
				variant="default"
				size="default"
				separator=":"
			/>
		</div>
	);
}
```

## Troubleshooting Guide

### Common Issues & Solutions

1. **Authentication Errors**

   ```
   Error: Invalid or missing API token

   ```

   - Verify your token in `.env.local`
   - Check if `ClocProvider` has access to the token
   - Ensure token has required permissions

2. **Component Styling Issues**

   ```
   Warning: Prop `className` did not match

   ```

   - Add required CSS imports
   - Check Tailwind configuration
   - Verify theme provider setup

3. **Timer Synchronization Problems**

   ```shell
   Error: Failed to sync timer state

   ```

   - Check network connectivity
   - Verify API endpoint configuration
   - Ensure token has timer permissions

### Getting Help

Need additional support? We're here to help:

- 📚 [Documentation](https://docs.cloc.ai/)
- 💬 [Discord Community](https://discord.gg/cloc-ai)
- 📧 [Email Support](mailto:<EMAIL>)

## Next Steps

- Explore our [Component Documentation](https://docs.cloc.ai/docs/components)
- Check out [Advanced Timer Features](https://docs.cloc.ai/docs/timers)
- Learn about [Team Management](https://docs.cloc.ai/teams)
<!-- - Discover [Analytics Integration](https://docs.cloc.ai/analytics) -->

Start building amazing time-tracking experiences with Cloc SDK! 🚀
