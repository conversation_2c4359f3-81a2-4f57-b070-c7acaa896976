---
id: ui
title: UI Components
sidebar_label: UI Components
sidebar_position: 8
---

I'll create a detailed documentation for each UI component with thorough explanations.

# UI Components Documentation

## Overview

The Ever® Cloc™ Platform UI components are organized into several categories, each designed for specific use cases and functionality.

## Buttons

### Button Variants

![Cloc Button Default](../assets/component/ui_button.png)

1. **Default Button**

- Basic button component with default styling
- Used for general actions

```tsx
import { ClocButton } from "@cloc/atoms";

<ClocButton>Default Button</ClocButton>;
```

2. **Primary Button**

- Emphasized button for primary actions
- Uses the theme's primary color

```tsx
import { ClocButton } from "@cloc/atoms";

<ClocButton variant="default">Primary Button</ClocButton>;
```

3. **Secondary Button**

- Less emphasized button for secondary actions
- Useful for alternative options

```tsx
import { ClocButton } from "@cloc/atoms";

<ClocButton variant="secondary">Secondary Button</ClocButton>;
```

4. **Outline Button**

- Bordered button with transparent background
- Ideal for less prominent actions

```tsx
import { ClocButton } from "@cloc/atoms";

<ClocButton variant="outline">Outline Button</ClocButton>;
```

### Button Props

```tsx
interface ClocButtonProps {
  variant?: "default" | "secondary" | "outline";
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
}
```

## Forms

### Form Types

1. **Basic Form (`ClocForm`)**

![Cloc Form Basic](../assets/component/ui_form.png)

- Standard form component
- Includes common form elements
- Built-in validation

```tsx
import { ClocForm } from "@cloc/atoms";

<ClocForm />;
```

2. **Auth Form (`AuthForm`)**

![Cloc Form Auth](../assets/component/ui_auth.png)

- Specialized form for authentication
- Includes username/email and password fields
- Built-in validation and error handling

```tsx
import { AuthForm } from "@cloc/atoms";

<AuthForm source="storybook" />;
```

## Tables

![Cloc Table Basic](../assets/component/ui_table.png)

### Table Features

- Sortable columns
- Custom cell rendering
- Responsive design
- Dark mode support
- Optional footer
- Customizable styles

### Table Usage

```tsx
import { ClocTable } from "@cloc/atoms";

const data = [
  {
    id: 1,
    date: "2024-03-20",
    project: "Project A",
    duration: "2h 30m",
    status: "Completed",
  },
  {
    id: 2,
    date: "2024-03-21",
    project: "Project B",
    duration: "4h 15m",
    status: "In Progress",
  },
  {
    id: 3,
    date: "2024-03-22",
    project: "Project C",
    duration: "1h 45m",
    status: "Pending",
  },
  {
    id: 4,
    date: "2024-03-23",
    project: "Project D",
    duration: "3h 00m",
    status: "Completed",
  },
  {
    id: 5,
    date: "2024-03-24",
    project: "Project E",
    duration: "5h 20m",
    status: "In Progress",
  },
];

<ClocTable
  data={data}
  caption="Project Timeline"
  renderHeader={(column) => column.toUpperCase()}
/>;
```

### Table Props

```tsx
interface ClocTableProps {
  data: any[];
  caption?: string;
  footerData?: FooterData | null;
  renderHeader?: (column: string) => React.ReactNode;
  renderCell?: (row: any, column: string) => React.ReactNode;
  tableClassName?: string;
  headerClassName?: string;
  rowClassName?: string;
  cellClassName?: string;
  footerClassName?: string;
}
```

## Toggles

### Toggle Types

1. **Theme Toggle (`ClocThemeToggle`)**

![Cloc Toggle Theme](../assets/component/ui_theme-toggle.png)

- Switches between light and dark themes
- Persists selection in local storage
- Smooth transition animations

```tsx
import { ClocThemeToggle } from "@cloc/atoms";

<ClocThemeToggle />;
```

2. **Font Toggle (`ClocFontToggle`)**

![Cloc Toggle Font](../assets/component/ui_font-toggle.png)

- Switches between different font families
- Maintains consistency across the application
- Accessible design

```tsx
import { ClocFontToggle } from "@cloc/atoms";

<ClocFontToggle />;
```

### Toggle Props

```tsx
interface ToggleProps {
  defaultValue?: boolean;
  onChange?: (value: boolean) => void;
  disabled?: boolean;
  className?: string;
}
```

## Best Practices

1. **Component Wrapping**

```tsx
import { ClocProvider } from "@cloc/atoms";

function App() {
  return <ClocProvider>{/* Your components */}</ClocProvider>;
}
```

2. **Accessibility**

- Use semantic HTML elements
- Include ARIA labels
- Maintain keyboard navigation
- Provide proper contrast ratios

3. **Responsive Design**

- Use provided size variants
- Test on different viewports
- Implement proper spacing
- Consider mobile interactions

4. **Error Handling**

- Implement form validation
- Show meaningful error messages
- Provide user feedback
- Handle edge cases

5. **Performance**

- Use dynamic imports for large components
- Implement proper memoization
- Optimize re-renders
- Consider code splitting

## Examples

Visit our [example site](https://kit-examples-base.cloc.ai/components/ui) to see interactive examples of all components.
