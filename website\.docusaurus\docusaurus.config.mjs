/*
 * AUTOGENERATED - DON'T EDIT
 * Your edits in this file will be overwritten in the next build!
 * Modify the docusaurus.config.js file at your site's root instead.
 */
export default {
  "plugins": [
    null,
    [
      "C:\\Users\\<USER>\\Desktop\\Projects\\Ever Tech\\ever-cloc-docs\\website\\node_modules\\@easyops-cn\\docusaurus-search-local\\dist\\server\\server\\index.js",
      {
        "hashed": true
      }
    ]
  ],
  "scripts": [
    {
      "src": "https://buttons.github.io/buttons.js",
      "async": true
    }
  ],
  "title": "Ever Cloc",
  "tagline": "Open Time Tracking and Location Tracking Platform",
  "favicon": "img/favicon.ico",
  "url": "https://docs.cloc.ai",
  "baseUrl": "/",
  "organizationName": "ever-co",
  "projectName": "ever-cloc-docs",
  "onBrokenLinks": "warn",
  "onBrokenMarkdownLinks": "warn",
  "staticDirectories": [
    "./docs/assets",
    "static"
  ],
  "i18n": {
    "path": "./docs/i18n/",
    "defaultLocale": "en",
    "locales": [
      "en",
      "fr",
      "ar",
      "bg",
      "zh",
      "nl",
      "de",
      "he",
      "it",
      "pl",
      "pt",
      "ru",
      "es"
    ],
    "localeConfigs": {}
  },
  "presets": [
    [
      "classic",
      {
        "docs": {
          "exclude": [
            "**/i18n/**"
          ],
          "sidebarPath": "./sidebars.ts",
          "path": "./docs/",
          "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/"
        },
        "theme": {
          "customCss": "./src/css/custom.css"
        }
      }
    ]
  ],
  "themeConfig": {
    "image": "/overview.png",
    "colorMode": {
      "defaultMode": "dark",
      "disableSwitch": false,
      "respectPrefersColorScheme": false
    },
    "navbar": {
      "style": "dark",
      "logo": {
        "alt": "Ever® Cloc Logo",
        "srcDark": "/img/ever-cloc.svg",
        "src": "img/ever-cloc-dark.svg"
      },
      "items": [
        {
          "type": "docSidebar",
          "sidebarId": "tutorialSidebar",
          "position": "left",
          "to": "/docs",
          "label": "Docs"
        },
        {
          "to": "/help",
          "label": "Help",
          "position": "left"
        },
        {
          "to": "/docs/advanced-guide/support",
          "label": "Support",
          "position": "left"
        },
        {
          "type": "localeDropdown",
          "position": "right",
          "className": "header-locale-link",
          "dropdownItemsBefore": [],
          "dropdownItemsAfter": []
        },
        {
          "href": "https://github.com/ever-co/ever-cloc",
          "label": "GitHub",
          "position": "right",
          "className": "header-github-link"
        }
      ],
      "hideOnScroll": false
    },
    "footer": {
      "style": "dark",
      "logo": {
        "src": "/img/ever-cloc.svg",
        "height": 40
      },
      "links": [
        {
          "title": "Docs",
          "items": [
            {
              "label": "Introduction",
              "to": "/docs/introduction"
            }
          ]
        },
        {
          "title": "Community",
          "items": [
            {
              "label": "User Showcases",
              "href": "/users"
            },
            {
              "label": "Stack Overflow",
              "href": "https://stackoverflow.com/questions/tagged/ever-cloc"
            },
            {
              "label": "Gitter Chat",
              "href": "https://gitter.im/ever-co/ever-cloc"
            },
            {
              "label": "Discord Chat",
              "href": "https://discord.com/invite/msqRJ4w"
            },
            {
              "label": "Twitter",
              "href": "https://twitter.com/gauzyplatform"
            }
          ]
        },
        {
          "title": "More",
          "items": [
            {
              "label": "GitHub",
              "href": "https://github.com/ever-co/ever-cloc"
            },
            {
              "html": "\n                <div class=\"widget\"><a class=\"btn\" href=\"https://github.com/ever-co/ever-cloc\" rel=\"noopener\" target=\"_blank\" aria-label=\"Star this project on GitHub\"><svg viewBox=\"0 0 16 16\" width=\"14\" height=\"14\" class=\"octicon octicon-star\" aria-hidden=\"true\"><path d=\"M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z\"></path></svg>&nbsp;<span>Star</span></a><a class=\"social-count\" href=\"https://github.com/ever-co/ever-cloc/stargazers\" rel=\"noopener\" target=\"_blank\" aria-label=\"100+ stargazers on GitHub\">100</a></div>"
            }
          ]
        }
      ],
      "copyright": "Copyright © 2023-2024 Ever Co. LTD."
    },
    "prism": {
      "theme": {
        "plain": {
          "color": "#393A34",
          "backgroundColor": "#f6f8fa"
        },
        "styles": [
          {
            "types": [
              "comment",
              "prolog",
              "doctype",
              "cdata"
            ],
            "style": {
              "color": "#999988",
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "namespace"
            ],
            "style": {
              "opacity": 0.7
            }
          },
          {
            "types": [
              "string",
              "attr-value"
            ],
            "style": {
              "color": "#e3116c"
            }
          },
          {
            "types": [
              "punctuation",
              "operator"
            ],
            "style": {
              "color": "#393A34"
            }
          },
          {
            "types": [
              "entity",
              "url",
              "symbol",
              "number",
              "boolean",
              "variable",
              "constant",
              "property",
              "regex",
              "inserted"
            ],
            "style": {
              "color": "#36acaa"
            }
          },
          {
            "types": [
              "atrule",
              "keyword",
              "attr-name",
              "selector"
            ],
            "style": {
              "color": "#00a4db"
            }
          },
          {
            "types": [
              "function",
              "deleted",
              "tag"
            ],
            "style": {
              "color": "#d73a49"
            }
          },
          {
            "types": [
              "function-variable"
            ],
            "style": {
              "color": "#6f42c1"
            }
          },
          {
            "types": [
              "tag",
              "selector",
              "keyword"
            ],
            "style": {
              "color": "#00009f"
            }
          }
        ]
      },
      "darkTheme": {
        "plain": {
          "color": "#F8F8F2",
          "backgroundColor": "#282A36"
        },
        "styles": [
          {
            "types": [
              "prolog",
              "constant",
              "builtin"
            ],
            "style": {
              "color": "rgb(189, 147, 249)"
            }
          },
          {
            "types": [
              "inserted",
              "function"
            ],
            "style": {
              "color": "rgb(80, 250, 123)"
            }
          },
          {
            "types": [
              "deleted"
            ],
            "style": {
              "color": "rgb(255, 85, 85)"
            }
          },
          {
            "types": [
              "changed"
            ],
            "style": {
              "color": "rgb(255, 184, 108)"
            }
          },
          {
            "types": [
              "punctuation",
              "symbol"
            ],
            "style": {
              "color": "rgb(248, 248, 242)"
            }
          },
          {
            "types": [
              "string",
              "char",
              "tag",
              "selector"
            ],
            "style": {
              "color": "rgb(255, 121, 198)"
            }
          },
          {
            "types": [
              "keyword",
              "variable"
            ],
            "style": {
              "color": "rgb(189, 147, 249)",
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "comment"
            ],
            "style": {
              "color": "rgb(98, 114, 164)"
            }
          },
          {
            "types": [
              "attr-name"
            ],
            "style": {
              "color": "rgb(241, 250, 140)"
            }
          }
        ]
      },
      "additionalLanguages": [],
      "magicComments": [
        {
          "className": "theme-code-block-highlighted-line",
          "line": "highlight-next-line",
          "block": {
            "start": "highlight-start",
            "end": "highlight-end"
          }
        }
      ]
    },
    "docs": {
      "versionPersistence": "localStorage",
      "sidebar": {
        "hideable": false,
        "autoCollapseCategories": false
      }
    },
    "blog": {
      "sidebar": {
        "groupByYear": true
      }
    },
    "metadata": [],
    "tableOfContents": {
      "minHeadingLevel": 2,
      "maxHeadingLevel": 3
    }
  },
  "baseUrlIssueBanner": true,
  "future": {
    "experimental_faster": {
      "swcJsLoader": false,
      "swcJsMinimizer": false,
      "swcHtmlMinimizer": false,
      "lightningCssMinimizer": false,
      "mdxCrossCompilerCache": false,
      "rspackBundler": false
    },
    "experimental_storage": {
      "type": "localStorage",
      "namespace": false
    },
    "experimental_router": "browser"
  },
  "onBrokenAnchors": "warn",
  "onDuplicateRoutes": "warn",
  "customFields": {},
  "themes": [],
  "headTags": [],
  "stylesheets": [],
  "clientModules": [],
  "titleDelimiter": "|",
  "noIndex": false,
  "markdown": {
    "format": "mdx",
    "mermaid": false,
    "mdx1Compat": {
      "comments": true,
      "admonitions": true,
      "headingIds": true
    },
    "anchors": {
      "maintainCase": false
    }
  }
};
