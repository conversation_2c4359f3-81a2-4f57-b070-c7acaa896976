{"allContent": {"docusaurus-plugin-content-docs": {"default": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/docs", "tagsPath": "/docs/tags", "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs", "editUrlLocalized": "https://github.com/ever-co/ever-cloc/tree/main/docs/i18n/en/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "C:\\Users\\<USER>\\Desktop\\Projects\\Ever Tech\\ever-cloc-docs\\website\\sidebars.ts", "contentPath": "C:\\Users\\<USER>\\Desktop\\Projects\\Ever Tech\\ever-cloc-docs\\website\\docs", "contentPathLocalized": "C:\\Users\\<USER>\\Desktop\\Projects\\Ever Tech\\ever-cloc-docs\\website\\docs\\i18n\\en\\docusaurus-plugin-content-docs\\current", "docs": [{"id": "api/api", "title": "API Reference", "description": "", "source": "@site/docs/api/index.md", "sourceDirName": "api", "slug": "/api/", "permalink": "/docs/api/", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/api/index.md", "tags": [], "version": "current", "sidebarPosition": 6, "frontMatter": {"id": "api", "title": "API Reference", "sidebar_label": "API Reference", "sidebar_position": 6}, "sidebar": "tutorialSidebar", "previous": {"title": "UI Components", "permalink": "/docs/components/ui"}, "next": {"title": "API Reference", "permalink": "/docs/api/api-reference"}}, {"id": "api/api-docs", "title": "API Documentations", "description": "1. Authentication Endpoints", "source": "@site/docs/api/api-docs.md", "sourceDirName": "api", "slug": "/api/api-docs", "permalink": "/docs/api/api-docs", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/api/api-docs.md", "tags": [], "version": "current", "sidebarPosition": 8, "frontMatter": {"id": "api-docs", "title": "API Documentations", "sidebar_label": "API Documentations", "sidebar_position": 8}, "sidebar": "tutorialSidebar", "previous": {"title": "API Reference", "permalink": "/docs/api/api-reference"}, "next": {"title": "Examples & Tutorials", "permalink": "/docs/examples-tutorials"}}, {"id": "api/api-reference", "title": "API Reference", "description": "Core Components", "source": "@site/docs/api/api-reference.md", "sourceDirName": "api", "slug": "/api/api-reference", "permalink": "/docs/api/api-reference", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/api/api-reference.md", "tags": [], "version": "current", "sidebarPosition": 6, "frontMatter": {"id": "api-reference", "title": "API Reference", "sidebar_label": "API Reference", "sidebar_position": 6}, "sidebar": "tutorialSidebar", "previous": {"title": "API Reference", "permalink": "/docs/api/"}, "next": {"title": "API Documentations", "permalink": "/docs/api/api-docs"}}, {"id": "cloc-sdk", "title": "Cloc SDK", "description": "Transform your productivity tools with Ever Cloc SDK, a powerful toolkit for building customized time tracking solutions. Whether you're developing a focused work timer, a project management system, or a comprehensive productivity suite, our SDK provides the building blocks you need.", "source": "@site/docs/cloc-sdk.mdx", "sourceDirName": ".", "slug": "/cloc-sdk", "permalink": "/docs/cloc-sdk", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/cloc-sdk.mdx", "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "cloc-sdk", "title": "Cloc SDK", "sidebar_label": "Cloc SDK", "sidebar_position": 2}, "sidebar": "tutorialSidebar", "previous": {"title": "Introduction", "permalink": "/docs/index"}, "next": {"title": "Get Started", "permalink": "/docs/introduction/"}}, {"id": "components/components", "title": "Components Configuration", "description": "", "source": "@site/docs/components/index.md", "sourceDirName": "components", "slug": "/components/", "permalink": "/docs/components/", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/components/index.md", "tags": [], "version": "current", "sidebarPosition": 5, "frontMatter": {"id": "components", "title": "Components Configuration", "sidebar_label": "Components Configuration", "sidebar_position": 5}, "sidebar": "tutorialSidebar", "previous": {"title": "Cloc API", "permalink": "/docs/core-libraries/cloc-api"}, "next": {"title": "Timer Components", "permalink": "/docs/components/timer"}}, {"id": "components/reports", "title": "Reports", "description": "The Working Hours Reports feature provides various chart visualizations to help track and analyze working hours across team members. These charts are built using Recharts and offer different ways to view time-tracking data.", "source": "@site/docs/components/reports.md", "sourceDirName": "components", "slug": "/components/reports", "permalink": "/docs/components/reports", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/components/reports.md", "tags": [], "version": "current", "sidebarPosition": 7, "frontMatter": {"id": "reports", "title": "Reports", "sidebar_label": "Reports", "sidebar_position": 7}, "sidebar": "tutorialSidebar", "previous": {"title": "Timer Components", "permalink": "/docs/components/timer"}, "next": {"title": "UI Components", "permalink": "/docs/components/ui"}}, {"id": "components/timer", "title": "Timer Components", "description": "The Timer Components library provides a comprehensive suite of customizable timing solutions for React and Next.js applications. Built with TypeScript and modern React patterns, these components offer flexibility, performance, and seamless integration capabilities.", "source": "@site/docs/components/timer.md", "sourceDirName": "components", "slug": "/components/timer", "permalink": "/docs/components/timer", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/components/timer.md", "tags": [], "version": "current", "sidebarPosition": 6, "frontMatter": {"id": "timer", "title": "Timer Components", "sidebar_label": "Timer Components", "sidebar_position": 6}, "sidebar": "tutorialSidebar", "previous": {"title": "Components Configuration", "permalink": "/docs/components/"}, "next": {"title": "Reports", "permalink": "/docs/components/reports"}}, {"id": "components/ui", "title": "UI Components", "description": "I'll create a detailed documentation for each UI component with thorough explanations.", "source": "@site/docs/components/ui.md", "sourceDirName": "components", "slug": "/components/ui", "permalink": "/docs/components/ui", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/components/ui.md", "tags": [], "version": "current", "sidebarPosition": 8, "frontMatter": {"id": "ui", "title": "UI Components", "sidebar_label": "UI Components", "sidebar_position": 8}, "sidebar": "tutorialSidebar", "previous": {"title": "Reports", "permalink": "/docs/components/reports"}, "next": {"title": "API Reference", "permalink": "/docs/api/"}}, {"id": "core-libraries/cloc-api", "title": "Cloc API", "description": "@cloc/api is a powerful TypeScript library that provides seamless integration with Cloc backend services. It offers a comprehensive suite of async functions for time tracking, organization management, authentication, and reporting functionalities. Built with TypeScript, it ensures type safety and excellent developer experience.", "source": "@site/docs/core-libraries/cloc-api.md", "sourceDirName": "core-libraries", "slug": "/core-libraries/cloc-api", "permalink": "/docs/core-libraries/cloc-api", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/core-libraries/cloc-api.md", "tags": [], "version": "current", "sidebarPosition": 8, "frontMatter": {"id": "cloc-api", "title": "Cloc API", "sidebar_label": "Cloc API", "sidebar_position": 8}, "sidebar": "tutorialSidebar", "previous": {"title": "Cloc Types", "permalink": "/docs/core-libraries/cloc-types"}, "next": {"title": "Components Configuration", "permalink": "/docs/components/"}}, {"id": "core-libraries/cloc-atoms", "title": "Cloc Atoms", "description": "@cloc/atoms is a comprehensive React component library specializing in time tracking and data visualization. Built with TypeScript and modern React practices, it offers highly customizable components with built-in theme support, responsive design, and seamless integration capabilities.", "source": "@site/docs/core-libraries/cloc-atoms.md", "sourceDirName": "core-libraries", "slug": "/core-libraries/cloc-atoms", "permalink": "/docs/core-libraries/cloc-atoms", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/core-libraries/cloc-atoms.md", "tags": [], "version": "current", "sidebarPosition": 5, "frontMatter": {"id": "cloc-atoms", "title": "Cloc Atoms", "sidebar_label": "Cloc Atoms", "sidebar_position": 5}, "sidebar": "tutorialSidebar", "previous": {"title": "Core Libraries", "permalink": "/docs/core-libraries/"}, "next": {"title": "Cloc UI", "permalink": "/docs/core-libraries/cloc-ui"}}, {"id": "core-libraries/cloc-types", "title": "Cloc Types", "description": "@cloc/types is a comprehensive TypeScript definition package that provides type safety and IntelliSense support for the Cloc ecosystem. It serves as the foundation for type definitions across all Cloc components, utilities, and features.", "source": "@site/docs/core-libraries/cloc-types.md", "sourceDirName": "core-libraries", "slug": "/core-libraries/cloc-types", "permalink": "/docs/core-libraries/cloc-types", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/core-libraries/cloc-types.md", "tags": [], "version": "current", "sidebarPosition": 7, "frontMatter": {"id": "cloc-types", "title": "Cloc Types", "sidebar_label": "Cloc Types", "sidebar_position": 7}, "sidebar": "tutorialSidebar", "previous": {"title": "Cloc UI", "permalink": "/docs/core-libraries/cloc-ui"}, "next": {"title": "Cloc API", "permalink": "/docs/core-libraries/cloc-api"}}, {"id": "core-libraries/cloc-ui", "title": "Cloc UI", "description": "@cloc/ui is a comprehensive React component library that provides the foundational building blocks for creating consistent, accessible, and themeable user interfaces across Cloc applications. The library emphasizes reusability, accessibility, and modern design principles.", "source": "@site/docs/core-libraries/cloc-ui.md", "sourceDirName": "core-libraries", "slug": "/core-libraries/cloc-ui", "permalink": "/docs/core-libraries/cloc-ui", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/core-libraries/cloc-ui.md", "tags": [], "version": "current", "sidebarPosition": 6, "frontMatter": {"id": "cloc-ui", "title": "Cloc UI", "sidebar_label": "Cloc UI", "sidebar_position": 6}, "sidebar": "tutorialSidebar", "previous": {"title": "Cloc Atoms", "permalink": "/docs/core-libraries/cloc-atoms"}, "next": {"title": "Cloc Types", "permalink": "/docs/core-libraries/cloc-types"}}, {"id": "core-libraries/core-libraries", "title": "Core Libraries", "description": "", "source": "@site/docs/core-libraries/index.md", "sourceDirName": "core-libraries", "slug": "/core-libraries/", "permalink": "/docs/core-libraries/", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/core-libraries/index.md", "tags": [], "version": "current", "sidebarPosition": 4, "frontMatter": {"id": "core-libraries", "title": "Core Libraries", "sidebar_label": "Core Libraries", "sidebar_position": 4}, "sidebar": "tutorialSidebar", "previous": {"title": "Quick Start Examples", "permalink": "/docs/introduction/quick-start-example"}, "next": {"title": "Cloc Atoms", "permalink": "/docs/core-libraries/cloc-atoms"}}, {"id": "examples-tutorials", "title": "Examples & Tutorials", "description": "Basic Setup", "source": "@site/docs/examples-tutorials.md", "sourceDirName": ".", "slug": "/examples-tutorials", "permalink": "/docs/examples-tutorials", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/examples-tutorials.md", "tags": [], "version": "current", "sidebarPosition": 7, "frontMatter": {"id": "examples-tutorials", "title": "Examples & Tutorials", "sidebar_label": "Examples & Tutorials", "sidebar_position": 7}, "sidebar": "tutorialSidebar", "previous": {"title": "API Documentations", "permalink": "/docs/api/api-docs"}}, {"id": "index", "title": "Introduction", "description": "Welcome to the Ever Cloc documentation. This documentation will help you get started with Ever Cloc Platform, a comprehensive platform for time tracking and location tracking.", "source": "@site/docs/introduction.mdx", "sourceDirName": ".", "slug": "/index", "permalink": "/docs/index", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/introduction.mdx", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "Introduction", "sidebar_label": "Introduction", "sidebar_position": 1}, "sidebar": "tutorialSidebar", "next": {"title": "Cloc SDK", "permalink": "/docs/cloc-sdk"}}, {"id": "introduction/get-started", "title": "Get Started", "description": "", "source": "@site/docs/introduction/index.md", "sourceDirName": "introduction", "slug": "/introduction/", "permalink": "/docs/introduction/", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/introduction/index.md", "tags": [], "version": "current", "sidebarPosition": 3, "frontMatter": {"id": "get-started", "title": "Get Started", "sidebar_label": "Get Started", "sidebar_position": 3}, "sidebar": "tutorialSidebar", "previous": {"title": "Cloc SDK", "permalink": "/docs/cloc-sdk"}, "next": {"title": "Installation Guide", "permalink": "/docs/introduction/installation-guide"}}, {"id": "introduction/installation-guide", "title": "Installation Guide", "description": "Welcome to Cloc SDK! This comprehensive guide will help you integrate powerful time tracking and analytics capabilities into your Next.js application. Whether you're building a productivity tool, project management system, or team collaboration platform, Cloc SDK provides the building blocks you need.", "source": "@site/docs/introduction/installation-guide.md", "sourceDirName": "introduction", "slug": "/introduction/installation-guide", "permalink": "/docs/introduction/installation-guide", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/introduction/installation-guide.md", "tags": [], "version": "current", "sidebarPosition": 4, "frontMatter": {"id": "installation-guide", "title": "Installation Guide", "sidebar_label": "Installation Guide", "sidebar_position": 4}, "sidebar": "tutorialSidebar", "previous": {"title": "Get Started", "permalink": "/docs/introduction/"}, "next": {"title": "Quick Start Examples", "permalink": "/docs/introduction/quick-start-example"}}, {"id": "introduction/quick-start-example", "title": "Quick Start Examples", "description": "First, install the required packages using your preferred package manager:", "source": "@site/docs/introduction/quick-start-example.md", "sourceDirName": "introduction", "slug": "/introduction/quick-start-example", "permalink": "/docs/introduction/quick-start-example", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/introduction/quick-start-example.md", "tags": [], "version": "current", "sidebarPosition": 5, "frontMatter": {"id": "quick-start-example", "title": "Quick Start Examples", "sidebar_label": "Quick Start Examples", "sidebar_position": 5}, "sidebar": "tutorialSidebar", "previous": {"title": "Installation Guide", "permalink": "/docs/introduction/installation-guide"}, "next": {"title": "Core Libraries", "permalink": "/docs/core-libraries/"}}], "drafts": [], "sidebars": {"tutorialSidebar": [{"type": "doc", "id": "index", "label": "Introduction"}, {"type": "doc", "id": "cloc-sdk", "label": "Cloc SDK"}, {"type": "category", "label": "Get Started", "collapsible": true, "collapsed": true, "items": [{"type": "doc", "id": "introduction/installation-guide", "label": "Installation Guide"}, {"type": "doc", "id": "introduction/quick-start-example", "label": "Quick Start Examples"}], "link": {"type": "doc", "id": "introduction/get-started"}}, {"type": "category", "label": "Core Libraries", "collapsible": true, "collapsed": true, "items": [{"type": "doc", "id": "core-libraries/cloc-atoms", "label": "Cloc Atoms"}, {"type": "doc", "id": "core-libraries/cloc-ui", "label": "Cloc UI"}, {"type": "doc", "id": "core-libraries/cloc-types", "label": "Cloc Types"}, {"type": "doc", "id": "core-libraries/cloc-api", "label": "Cloc API"}], "link": {"type": "doc", "id": "core-libraries/core-libraries"}}, {"type": "category", "label": "Components Configuration", "collapsible": true, "collapsed": true, "items": [{"type": "doc", "id": "components/timer", "label": "Timer Components"}, {"type": "doc", "id": "components/reports", "label": "Reports"}, {"type": "doc", "id": "components/ui", "label": "UI Components"}], "link": {"type": "doc", "id": "components/components"}}, {"type": "category", "label": "API Reference", "collapsible": true, "collapsed": true, "items": [{"type": "doc", "id": "api/api-reference", "label": "API Reference"}, {"type": "doc", "id": "api/api-docs", "label": "API Documentations"}], "link": {"type": "doc", "id": "api/api"}}, {"type": "doc", "id": "examples-tutorials", "label": "Examples & Tutorials"}]}}]}}, "docusaurus-plugin-content-blog": {"default": {"blogSidebarTitle": "Recent posts", "blogPosts": [], "blogListPaginated": [], "blogTags": {}, "blogTagsListPath": "/blog/tags"}}, "docusaurus-plugin-content-pages": {"default": [{"type": "jsx", "permalink": "/help", "source": "@site/src/pages/help.tsx"}, {"type": "jsx", "permalink": "/", "source": "@site/src/pages/index.tsx"}, {"type": "mdx", "permalink": "/markdown-page", "source": "@site/src/pages/markdown-page.md", "title": "Markdown page example", "description": "You don't need React to write simple standalone pages.", "frontMatter": {"title": "Markdown page example"}, "unlisted": false}, {"type": "jsx", "permalink": "/users", "source": "@site/src/pages/users.tsx"}]}, "docusaurus-plugin-debug": {}, "docusaurus-theme-classic": {}, "@easyops-cn/docusaurus-search-local": {}, "docusaurus-bootstrap-plugin": {}, "docusaurus-mdx-fallback-plugin": {}}}