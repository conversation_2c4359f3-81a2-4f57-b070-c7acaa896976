---
id: timer
title: Timer Components
sidebar_label: Timer Components
sidebar_position: 6
---

# Timer Components Documentation

The Timer Components library provides a comprehensive suite of customizable timing solutions for React and Next.js applications. Built with TypeScript and modern React patterns, these components offer flexibility, performance, and seamless integration capabilities.

## Compatibility

- React 18.x and above
- Next.js 13.x and above (with App Router support)
- TypeScript 4.x and above

## Component Architecture

The Cloc Timer library provides three distinct timer component families, each designed for different use cases and complexity levels. Each family maintains consistent patterns while offering unique features and capabilities.

### 1. Basic Timer

The foundational timer component family, offering essential functionality with various styling options.
Key Features:

- Default, border, and gray variants
- Rounded and full-rounded options
- Icon support
- Progress indicators
- Control buttons

### 2. Cloc Base Timer

Enhanced timer components with additional styling and functionality.
Key Features:

- All Basic Timer features
- Progress tracking
- Customizable borders
- Gray and contained variants
- Rounded corner options

### 3. Modern Timer

Advanced timer components with modern design and interactive features.
Key Features:

- Multiple display modes (default, bordered, gray)
- Expandable and resizable options
- Draggable and draggable dividers
- Icon support
- Progress indicators
- Control buttons

## Timer Variants

### 1. Basic Timer

The Basic Timer provides a collection of simple, customizable timer components with various styles and features.

#### Overview

The Basic Timer component is part of the Cloc Kit UI library, offering a range of timer variants with different styles, borders, and colors. View live examples in our [example application](https://kit-examples-base.cloc.ai/components/timer/basic).

#### Component Categories

The Basic Timer is organized into four main categories:

1. Basic Variants
2. Icon Variants
3. Progress Variants
4. Button Variants

#### Available Variants

1. Default Style

![Basic Timer Default](../assets/component/basic-timer_basic.png)

```tsx
import { BasicTimer } from "@cloc/atoms";

<BasicTimer />;
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/basic)

2. Border Styles

![Basic Timer Border](../assets/component/basic-timer_basic-border.png)

```tsx
import { BasicTimerBorder, BasicTimerBorderRounded, BasicTimerBorderFullRounded } from '@cloc/atoms';
// Simple border
<BasicTimerBorder />

// Rounded border
<BasicTimerBorderRounded />

// Fully rounded border
<BasicTimerBorderFullRounded />
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/basic)

3. Gray Variants

![Basic Timer Gray](../assets/component/basic-timer_basic-gray.png)

```tsx
import { BasicTimerGray, BasicTimerGrayRounded, BasicTimerGrayFullRounded } from '@cloc/atoms';

// Gray background
<BasicTimerGray />

// Gray with rounded corners
<BasicTimerGrayRounded />

// Gray with fully rounded corners
<BasicTimerGrayFullRounded />
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/basic)

4. Contained Style

![Basic Timer Contained](../assets/component/basic-timer_basic-contained.png)

```tsx
import { BasicTimerContained, BasicTimerContainedRounded, BasicTimerContainedFullRounded } from '@cloc/atoms';

// Contained background
<BasicTimerContained />

// Contained with rounded corners
<BasicTimerContainedRounded />

// Contained with fully rounded corners
<BasicTimerContainedFullRounded />
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/basic)

#### Props

| Prop           | Type                                             | Default     | Description                   |
| -------------- | ------------------------------------------------ | ----------- | ----------------------------- |
| `variant`      | `'default' \| 'border' \| 'gray' \| 'contained'` | `'default'` | Visual style variant          |
| `rounded`      | `boolean`                                        | `false`     | Applies rounded corners       |
| `fullRounded`  | `boolean`                                        | `false`     | Applies fully rounded corners |
| `showProgress` | `boolean`                                        | `false`     | Shows progress indicator      |
| `showButtons`  | `boolean`                                        | `false`     | Shows control buttons         |
| `showIcon`     | `boolean`                                        | `false`     | Shows timer icon              |

#### Examples

1. Basic Implementation

```tsx
import { BasicTimer } from "@cloc/atoms";

function SimpleTimer() {
  return <BasicTimer />;
}
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/basic)

2. With Progress and Buttons

```tsx
import { BasicTimerIconProgressButton } from "@cloc/atoms";

function InteractiveTimer() {
  return <BasicTimerIconProgressButton />;
}
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/basic)

#### Demo

See all Basic Timer variants in action in our [example application](https://kit-examples-base.cloc.ai/components/timer/basic).

### 2. Cloc Base Timer

The Cloc Base Timer provides foundational timer components with various styles and features for building Cloc applications.

#### Overview

The Cloc Base Timer component is part of the Cloc Kit UI library, offering a comprehensive set of timer variants with different styles, icons, progress indicators, and button controls. View live examples in our [example application](https://kit-examples-base.cloc.ai/components/timer/cloc-base).

#### Basic Usage

![Cloc Base Timer Default](../assets/component/cloc-base_basic.png)

```tsx
import { ClocBasic } from "@cloc/atoms";

export default function MyTimer() {
  return <ClocBasic />;
}
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/basic)

#### Component Categories

The Cloc Base Timer is organized into four main categories:

1. Basic Variants
2. Icon Variants
3. Progress Variants
4. Button Variants

#### Basic Variants

Basic timer components with different visual styles:

1. Default Timer

![Cloc Base Timer Default](../assets/component/cloc-base_basic-default.png)

```tsx
import { ClocBasic } from "@cloc/atoms";

<ClocBasic />;
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/cloc-base)

2. Bordered Timer

![Cloc Base Timer Bordered](../assets/component/cloc-base_basic-border.png)

```tsx
import { ClocBasicBorder } from "@cloc/atoms";

<ClocBasicBorder />;
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/cloc-base)

3. Gray Timer

![Cloc Base Timer Gray](../assets/component/cloc-base_basic-gray.png)

```tsx
import { ClocBasicGray } from "@cloc/atoms";

<ClocBasicGray />;
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/cloc-base)

#### Props

| Prop          | Type                              | Default     | Description                   |
| ------------- | --------------------------------- | ----------- | ----------------------------- |
| `progress`    | `boolean`                         | `false`     | Shows progress indicator      |
| `icon`        | `ReactNode`                       | `undefined` | Custom icon element           |
| `variant`     | `'default' \| 'border' \| 'gray'` | `'default'` | Visual style variant          |
| `rounded`     | `boolean`                         | `false`     | Applies rounded corners       |
| `fullRounded` | `boolean`                         | `false`     | Applies fully rounded corners |
| `showButtons` | `boolean`                         | `false`     | Shows control buttons         |

#### Variants

1. Basic Style Options

- Default
- Border
- Border Rounded
- Border Full Rounded
- Gray
- Gray Rounded

2. With Icons

- Default with Icon
- Border with Icon
- Gray with Icon

3. With Progress

- Default with Progress
- Border with Progress
- Gray with Progress

4. With Buttons

- Default with Buttons
- Border with Buttons
- Gray with Buttons

#### Examples

1. Basic Timer

![Cloc Base Timer Basic](../assets/component/cloc-base_basic.png)

```tsx
import { ClocBasic } from "@cloc/atoms";

function BasicTimer() {
  return <ClocBasic progress={false} />;
}
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/cloc-base)

2. Timer with Icon

![Cloc Base Timer Icon](../assets/component/cloc-base_with-icons.png)

```tsx
import { ClocBasicIcon } from "@cloc/atoms";

function IconTimer() {
  return <ClocBasicIcon />;
}
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/cloc-base)

3. Timer with Progress

![Cloc Base Timer Progress](../assets/component/cloc-base_with-progress.png)

```tsx
import { ClocBasicIconProgress } from "@cloc/atoms";

function ProgressTimer() {
  return <ClocBasicIconProgress />;
}
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/cloc-base)

4. Timer with Buttons

![Cloc Base Timer Buttons](../assets/component/cloc-base_with-buttons.png)

```tsx
import { ClocBasicIconProgressButton } from "@cloc/atoms";

function ButtonTimer() {
  return <ClocBasicIconProgressButton />;
}
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/cloc-base)

#### Customization

1. Border Styles

```tsx
import { ClocBasicBorderRounded, ClocBasicBorderFullRounded } from "@cloc/atoms";

// Rounded borders
<ClocBasicBorderRounded />

// Fully rounded borders
<ClocBasicBorderFullRounded />
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/cloc-base)

2. Color Variants

```tsx
import { ClocBasicGray, ClocBasicGrayRounded } from "@cloc/atoms";

// Gray variant
<ClocBasicGray />

// Gray with rounded borders
<ClocBasicGrayRounded />
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/cloc-base)

#### Demo

See all Cloc Base Timer variants in action in our [example application](https://kit-examples-base.cloc.ai/components/timer/cloc-base).

### 3. Modern Timer (ModernCloc)

The Modern Timer is a sophisticated time tracking component that offers various display modes, sizes, and interactive features. It's designed to provide a modern, customizable timer interface for professional applications.

#### Overview

The Modern Timer component is part of the Cloc Kit UI library, providing an advanced timer interface with multiple variants and features. You can see live examples in our [example application](https://kit-examples-base.cloc.ai/components/timer/modern-timer).

#### Usage

```tsx
import { ModernCloc } from "@cloc/atoms";

export default function MyTimer() {
  return (
    <ModernCloc
      variant="default"
      size="default"
      expanded={false}
      showProgress={false}
    />
  );
}
```

#### Props

| Prop           | Type                        | Default     | Description                                  |
| -------------- | --------------------------- | ----------- | -------------------------------------------- |
| `variant`      | `'default' \| 'bordered'`   | `'default'` | Visual style variant of the timer            |
| `size`         | `'sm' \| 'default' \| 'lg'` | `'default'` | Size of the timer component                  |
| `expanded`     | `boolean`                   | `false`     | Shows expanded view with additional controls |
| `showProgress` | `boolean`                   | `false`     | Displays progress indicator                  |
| `resizable`    | `boolean`                   | `false`     | Enables resizing capability                  |
| `separator`    | `string`                    | `':'`       | Time separator character                     |
| `draggable`    | `boolean`                   | `true`      | Enables drag functionality                   |

#### Variants

1. Default Timer

The basic timer implementation with minimal configuration.

![Default Timer](../assets/component/modern-timer_basic.png)

```tsx
import { ModernCloc } from "@cloc/atoms";

function DefaultTimer() {
  return (
    <ModernCloc
      variant="default"
      size="default"
      expanded={false}
      showProgress={false}
    />
  );
}
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/modern-timer)

2. Timer with Progress

Enhanced timer with visual progress indication.

![Timer with Progress](../assets/component/modern-timer_progress.png)

```tsx
import { ModernCloc } from "@cloc/atoms";

function ProgressTimer() {
  return <ModernCloc variant="default" showProgress expanded={false} />;
}
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/modern-timer)

3. Expanded Timer with Progress

Full-featured timer with expanded controls and progress tracking.

![Expanded Timer with Progress](../assets/component/modern-timer_expanded.png)

```tsx
import { ModernCloc } from "@cloc/atoms";

function ExpandedTimer() {
  return <ModernCloc variant="default" showProgress expanded />;
}
```

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/modern-timer)

#### Customization

1. Theme Support

The Modern Timer component supports theme customization through the ClocThemeToggle component:

```tsx
import { ModernCloc, ClocThemeToggle } from "@cloc/atoms";

function ThemedTimer() {
  return (
    <div>
      <ClocThemeToggle />
      <ModernCloc variant="default" />
    </div>
  );
}
```

2. Size Variations

Available sizes: `sm`, `default`, `lg`

```tsx
<ModernCloc size="sm" />
<ModernCloc size="default" />
<ModernCloc size="lg" />
```

#### Demo

See the All Modern Timer components in action in our [example application](https://kit-examples-base.cloc.ai/components/timer/modern-timer).

## Best Practices for Using Timer Components

1. **Component Selection**

   - Use Basic Timer for simple time display needs
   - Choose Cloc Base Timer for enhanced styling requirements
   - Implement Modern Timer for interactive features

2. **Variant Consistency**

   - Use consistent variants within the same context
   - Maintain similar border styles across related components

3. **Progressive Enhancement**

   - Start with basic variants
   - Add features incrementally
   - Consider mobile-first approach

4. **Responsive Design**
   - Use appropriate sizes based on viewport
   - Implement grid layouts for responsive behavior
   - Consider different device requirements

## Accessibility

- Keyboard navigation support
- ARIA labels for timer values
- Screen reader announcements
- High contrast theme support

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- IE11 compatibility mode available
- Mobile browser optimized

## Performance Considerations

- Minimal bundle size
- Optimized rendering
- Efficient state management
- Responsive to system resources
